@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-nunito-sans);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.97 0 0); /* Default colorless - light gray */
  --primary-foreground: oklch(0.145 0 0);
  --secondary: oklch(0.5 0.25 270); /* Purple */
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.55 0 0); /* Much brighter for better visibility */
  --accent: oklch(0.5 0.25 240); /* Blue */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.97 0 0); /* Default colorless */
  --sidebar-primary-foreground: oklch(0.145 0 0);
  --sidebar-accent: oklch(0.5 0.25 240); /* Blue */
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.269 0 0); /* Default colorless - dark gray */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.6 0.3 270); /* Bright Purple */
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.75 0 0); /* Brighter for better visibility in dark mode */
  --accent: oklch(0.6 0.3 240); /* Bright Blue */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.269 0 0); /* Default colorless */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.6 0.3 240); /* Bright Blue */
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Typography with Nunito Sans and lighter font weights */
  h1, h2, h3, h4, h5, h6 {
    @apply font-sans font-normal;
  }

  /* Large display titles for special use cases */
  .display-title {
    @apply text-5xl font-extralight tracking-tight;
  }

  .display-subtitle {
    @apply text-3xl font-light tracking-tight;
  }

  /* Page-level headings */
  .page-title {
    @apply text-4xl font-light tracking-tight;
  }

  .section-title {
    @apply text-3xl font-light tracking-tight;
  }

  .subsection-title {
    @apply text-2xl font-light tracking-tight;
  }

  .content-title {
    @apply text-xl font-normal;
  }


}

/* Custom gradient styles */
.gradient-red-purple {
  @apply bg-gradient-to-r from-primary via-secondary to-accent;
}

.gradient-purple-blue {
  @apply bg-gradient-to-r from-secondary to-accent;
}

.gradient-red-blue {
  @apply bg-gradient-to-r from-primary to-accent;
}

.gradient-text {
  @apply bg-clip-text text-transparent;
}

.gradient-border {
  @apply border border-transparent bg-origin-border;
  position: relative;
}

.gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(to right, var(--primary), var(--secondary), var(--accent));
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.gradient-border-muted {
  @apply border border-transparent bg-origin-border;
  position: relative;
}

.gradient-border-muted::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    to right,
    color-mix(in srgb, #f8f9fa 60%, var(--primary) 40%),
    color-mix(in srgb, #e9ecef 50%, var(--secondary) 50%),
    color-mix(in srgb, #f8f9fa 60%, var(--accent) 40%)
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.gradient-border-dynamic {
  @apply border border-transparent bg-origin-border;
  position: relative;
}

.gradient-border-dynamic::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    to right,
    color-mix(in srgb, var(--list-color) 60%, var(--primary) 40%),
    color-mix(in srgb, var(--list-color) 50%, var(--secondary) 50%),
    color-mix(in srgb, var(--list-color) 60%, var(--accent) 40%)
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

/* Container with max-width for desktop */
.container-max-width {
  width: 100%;
  max-width: 640px; /* Matches a typical mobile width */
  margin-left: auto;
  margin-right: auto;
}

/* Static Spotlight Card Effects - Resource Efficient */
/* Note: Spotlight effects are now handled via inline styles in the component for better performance */

/* Glass Card Effects */
.glass-card {
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  --glass-backdrop-blur: 12px;
  --glass-noise-opacity: 0.02;

  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-backdrop-blur)) saturate(1.2);
  -webkit-backdrop-filter: blur(var(--glass-backdrop-blur)) saturate(1.2);
  border: 1px solid var(--glass-border);
  box-shadow:
    0 1px 3px var(--glass-shadow),
    0 1px 2px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

/* Glass intensity variations */
.glass-card[data-glass-intensity="subtle"] {
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(255, 255, 255, 0.06);
  --glass-backdrop-blur: 8px;
  --glass-noise-opacity: 0.015;
}

.glass-card[data-glass-intensity="medium"] {
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-backdrop-blur: 12px;
  --glass-noise-opacity: 0.02;
}

.glass-card[data-glass-intensity="strong"] {
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-backdrop-blur: 16px;
  --glass-noise-opacity: 0.025;
}

/* Glass noise texture for subtle grain effect */
.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
  background-size: 20px 20px;
  opacity: var(--glass-noise-opacity);
  pointer-events: none;
  border-radius: inherit;
}

/* Dark mode glass effects */
.dark .glass-card {
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-shadow: rgba(0, 0, 0, 0.3);

  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  box-shadow:
    0 1px 3px var(--glass-shadow),
    0 1px 2px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dark .glass-card[data-glass-intensity="subtle"] {
  --glass-bg: rgba(255, 255, 255, 0.02);
  --glass-border: rgba(255, 255, 255, 0.05);
  --glass-noise-opacity: 0.01;
}

.dark .glass-card[data-glass-intensity="medium"] {
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-noise-opacity: 0.015;
}

.dark .glass-card[data-glass-intensity="strong"] {
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.12);
  --glass-noise-opacity: 0.02;
}

/* Enhanced readability for glass cards */
.glass-card .text-muted-foreground {
  color: hsl(var(--muted-foreground) / 0.9);
}

.dark .glass-card .text-muted-foreground {
  color: hsl(var(--muted-foreground) / 0.95);
}

/* Ensure text remains highly readable */
.glass-card {
  color: hsl(var(--card-foreground) / 0.95);
}

.dark .glass-card {
  color: hsl(var(--card-foreground) / 0.98);
}

/* Glass card hover effects */
.glass-card:hover {
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.dark .glass-card:hover {
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.12);
}

/* Space name container - enhanced glass styling */
.space-name-glass {
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  --glass-backdrop-blur: 12px;
  --glass-noise-opacity: 0.02;

  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-backdrop-blur)) saturate(1.2);
  -webkit-backdrop-filter: blur(var(--glass-backdrop-blur)) saturate(1.2);
  border: 1px solid var(--glass-border);
  box-shadow:
    0 1px 3px var(--glass-shadow),
    0 1px 2px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

/* Space name container hover - even more enhanced */
.space-name-glass:hover {
  --glass-bg: rgba(255, 255, 255, 0.12);
  --glass-border: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 2px 6px var(--glass-shadow),
    0 1px 3px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Dark mode space name container */
.dark .space-name-glass {
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.12);
  --glass-shadow: rgba(0, 0, 0, 0.3);
}

.dark .space-name-glass:hover {
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.18);
}

/* Glass card selection states */
.glass-card[data-selected="true"] {
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 2px 8px var(--glass-shadow),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.dark .glass-card[data-selected="true"] {
  --glass-bg: rgba(255, 255, 255, 0.06);
  --glass-border: rgba(255, 255, 255, 0.15);
}

/* Soft task selection states - subtle white/grey highlight */
.task-selected {
  background-color: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.08),
    0 1px 2px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.dark .task-selected {
  background-color: rgba(255, 255, 255, 0.04) !important;
  border-color: rgba(255, 255, 255, 0.18) !important;
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.15),
    0 1px 2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.12) !important;
}

/* Original dragged card is hidden - glass effect only applies to DragOverlay */
.glass-card[data-dragging="true"] {
  /* Hide the original card completely */
  opacity: 0 !important;
  visibility: hidden;
}

/* Glass Checkbox - "hole in the glass" effect */
.glass-checkbox {
  border-color: rgba(255, 255, 255, 0.1) !important;
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.15),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.08) !important;
}

.dark .glass-checkbox {
  border-color: rgba(255, 255, 255, 0.08) !important;
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    inset 0 0 0 1px rgba(255, 255, 255, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

/* Glass Radio Selector - "hole in the glass" effect for selection mode */
.glass-radio-selector {
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.15),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.08) !important;
}

.dark .glass-radio-selector {
  border: 2px solid rgba(255, 255, 255, 0.08) !important;
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    inset 0 0 0 1px rgba(255, 255, 255, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

/* Glass Radio Dot - subtle glow effect for selected state */
.glass-radio-dot {
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow:
    0 0 4px rgba(255, 255, 255, 0.3),
    inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.dark .glass-radio-dot {
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow:
    0 0 4px rgba(255, 255, 255, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

/* Mobile Modal and Virtual Keyboard Support */
@supports (height: 100dvh) {
  .mobile-modal-content {
    height: 100dvh;
  }
}

/* Virtual Keyboard Support */
@supports (bottom: env(keyboard-inset-height)) {
  .mobile-modal-keyboard-aware {
    bottom: max(0px, env(keyboard-inset-height, 0px));
    max-height: calc(70dvh - env(keyboard-inset-height, 0px));
  }
}

/* Scrollbar Hide Utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Mobile Touch and Scroll Optimization */
.mobile-scroll-container {
  -webkit-overflow-scrolling: touch;
  touch-action: pan-x;
  overscroll-behavior-x: contain;
  scroll-behavior: smooth;
}

.mobile-button-fixed {
  touch-action: manipulation;
  pointer-events: auto;
  position: relative;
  z-index: 50;
  -webkit-tap-highlight-color: transparent;
}

/* Draggable elements within scroll containers */
.draggable-in-scroll {
  touch-action: pan-x; /* Allow horizontal scrolling while enabling drag */
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

/* Prevent scroll momentum interference */
.scroll-momentum-fix {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;
}

/* Date Picker Styles */
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: var(--primary);
  --rdp-background-color: var(--primary);
  --rdp-accent-color-dark: var(--primary);
  --rdp-background-color-dark: var(--primary);
  --rdp-outline: 2px solid var(--ring);
  --rdp-outline-selected: 2px solid var(--primary);
  margin: 1rem 0;
}

.rdp-day_selected,
.rdp-day_selected:focus-visible,
.rdp-day_selected:hover {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
  background-color: var(--muted);
}

/* Drag and Drop Styles */
.drag-overlay {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
  opacity: 1;
  z-index: 1000;
  /* Ensure consistent text rendering in overlay */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Maintain original styling */
  pointer-events: none;
  /* Make it clear this is being dragged */
  transform: rotate(2deg) scale(1.02);
  transition: none;
}

/* Global cursor change during drag operations */
.dnd-context-dragging {
  cursor: grabbing !important;
}

/* Dim background cards during drag to make dragged card stand out */
.dnd-context-dragging .glass-card:not([data-dragging="true"]) {
  opacity: 0.6 !important;
  transition: opacity 200ms ease-out !important;
  /* Add subtle visual feedback for drop zones */
  position: relative;
}

/* Add drop zone indicator */
.dnd-context-dragging .glass-card:not([data-dragging="true"]):hover {
  opacity: 0.8 !important;
  transform: translateY(-1px);
  transition: all 150ms ease-out !important;
}

/* Hide the original dragged card completely - only show the DragOverlay version */
.dnd-context-dragging .glass-card[data-dragging="true"] {
  opacity: 0 !important;
  transition: none !important;
  /* Keep it in layout but invisible */
  visibility: hidden;
}

.dark .drag-overlay {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
}

/* Specific styles for list navigation drag overlay */
.list-drag-overlay {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  opacity: 1; /* Fully opaque, no see-through effect */
  z-index: 1000;
  /* Ensure consistent text rendering in overlay */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent size changes during drag for list tabs */
  width: max-content;
  min-width: fit-content;
  /* Maintain original styling */
  pointer-events: none;
}

.dark .list-drag-overlay {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Specific styles for task drag overlay to handle tall parent tasks */
.task-drag-overlay {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  opacity: 1;
  z-index: 1000;
  /* Ensure consistent text rendering in overlay */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Constrain height for very tall parent tasks */
  max-height: 70vh;
  overflow: hidden;
  /* Maintain original styling */
  pointer-events: none;
  /* Ensure proper width and prevent warping */
  width: 100%;
  max-width: 100%;
  min-width: 300px; /* Minimum width to prevent compression */
  /* Prevent layout shifts during drag */
  box-sizing: border-box;
  /* Maintain aspect ratio */
  contain: layout style;
  /* Make it clear this is being dragged */
  transform: rotate(1.5deg) scale(1.03);
  transition: none;
  /* Enhanced glass effect for dragged card */
  -webkit-backdrop-filter: blur(12px) saturate(1.3);
  backdrop-filter: blur(12px) saturate(1.3);
  /* Add subtle glow effect to make it stand out */
  border: 1px solid rgba(255, 255, 255, 0.2);
  outline: 2px solid rgba(59, 130, 246, 0.3);
  outline-offset: -1px;
}

.dark .task-drag-overlay {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}



/* Tab Underline Styles - Bidirectional Animation */

/* Active tab underline - visible and scaled */
.tab-with-underline::after {
  content: '';
  position: absolute;
  bottom: 4px; /* Equivalent to bottom-1 (4px) */
  left: 50%;
  width: 66%; /* 66% of the button width */
  height: 2px; /* Equivalent to h-0.5 (2px) */
  background-color: var(--active-underline-color, #6b7280); /* Much brighter fallback color */
  transform: translateX(-50%) scaleX(1);
  transform-origin: var(--underline-animation-origin, center);
  opacity: 1;
  transition: transform 300ms ease-out, opacity 300ms ease-out;
  border-radius: 1px;
}

/* Base state for all tab buttons - hidden underline */
button::after {
  content: '';
  position: absolute;
  bottom: 4px;
  left: 50%;
  width: 66%;
  height: 2px;
  background-color: var(--active-underline-color, #6b7280);
  transform: translateX(-50%) scaleX(0);
  transform-origin: var(--underline-animation-origin, center);
  opacity: 0;
  transition: transform 300ms ease-out, opacity 300ms ease-out;
  border-radius: 1px;
}

/* Departing tab underline - animate out in reverse direction */
[data-tab-id][data-departing="true"]::after {
  transform: translateX(-50%) scaleX(0);
  opacity: 0;
  transform-origin: var(--departure-animation-origin, center);
  transition: transform 150ms ease-in, opacity 150ms ease-in;
}

/* Arriving tab underline - animate in from direction */
[data-tab-id][data-arriving="true"]::after {
  transform: translateX(-50%) scaleX(1);
  opacity: 1;
  transform-origin: var(--underline-animation-origin, center);
  transition: transform 150ms ease-out 150ms, opacity 150ms ease-out 150ms;
}

/* Hide underline during drag operations */
.list-tab-dragging button.tab-with-underline::after,
.opacity-0 button.tab-with-underline::after {
  opacity: 0 !important;
  transform: translateX(-50%) scaleX(0) !important;
  transition: none !important; /* Disable transitions during drag */
}

.drag-handle {
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Disable all transitions for sortable elements to ensure instant positioning */
[data-dnd-kit-sortable] {
  transition: none !important;
}

/* Disable transitions for all elements during drag operations */
.dnd-context-dragging * {
  transition: none !important;
}

/* Ensure glass cards maintain their dimensions during drag but are hidden */
.glass-card[data-dragging="true"] {
  /* Prevent any layout shifts while keeping space */
  contain: layout style;
  /* Hide the original card - only DragOverlay should be visible */
  opacity: 0 !important;
  visibility: hidden;
}

/* Improved drag styling for list navigation tabs */
.list-tab-dragging {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  will-change: transform;
  transform-style: preserve-3d;
}

/* Container for horizontal scrolling with drag support */
.list-navigation-container {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.list-navigation-container::-webkit-scrollbar {
  display: none;
}

/* Custom text size for subtask elements */
.text-xxs {
  font-size: 10px;
  line-height: 1.2;
}


